# Tailwind CSS 集成配置文档

## 📋 概述

本文档详细说明了如何在 Nuxt3 项目中集成 Tailwind CSS，包括安装、配置、自定义和最佳实践。

## 🚀 快速开始

### 1. 安装依赖

```bash
# 使用 yarn 安装 Tailwind CSS 模块
yarn add @nuxtjs/tailwindcss

# 或使用 npm
npm install @nuxtjs/tailwindcss
```

### 2. 配置 Nuxt 模块

在 `nuxt.config.ts` 中添加 Tailwind CSS 模块：

```typescript
export default defineNuxtConfig({
  modules: [
    '@nuxt/eslint',
    '@nuxtjs/i18n',
    '@nuxtjs/tailwindcss'  // 添加这一行
  ],
  // ... 其他配置
})
```

### 3. 创建 Tailwind 配置文件

在项目根目录创建 `tailwind.config.js`：

```javascript
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./components/**/*.{js,vue,ts}",
    "./layouts/**/*.vue",
    "./pages/**/*.vue",
    "./plugins/**/*.{js,ts}",
    "./app.vue",
    "./error.vue"
  ],
  theme: {
    extend: {
      // 自定义配置
    },
  },
  plugins: [],
}
```

### 4. 配置 CSS 文件

在 `assets/css/main.css` 中添加 Tailwind 指令：

```css
/* Tailwind CSS 基础指令 */
@tailwind base;
@tailwind components;
@tailwind utilities;
```

## ⚙️ 详细配置

### Tailwind 配置文件详解

```javascript
/** @type {import('tailwindcss').Config} */
module.exports = {
  // 内容路径配置 - 告诉 Tailwind 在哪些文件中查找类名
  content: [
    "./components/**/*.{js,vue,ts}",
    "./layouts/**/*.vue",
    "./pages/**/*.vue",
    "./plugins/**/*.{js,ts}",
    "./app.vue",
    "./error.vue"
  ],
  
  // 主题配置
  theme: {
    extend: {
      // 自定义颜色
      colors: {
        primary: {
          50: '#eff6ff',
          100: '#dbeafe',
          200: '#bfdbfe',
          300: '#93c5fd',
          400: '#60a5fa',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
          800: '#1e40af',
          900: '#1e3a8a',
          950: '#172554',
        }
      },
      
      // 自定义字体
      fontFamily: {
        sans: ['Inter', 'ui-sans-serif', 'system-ui'],
      },
      
      // 自定义间距
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
      },
      
      // 自定义动画
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
      },
      
      // 关键帧定义
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
      },
    },
  },
  
  // 插件配置
  plugins: [
    // 可以添加官方或第三方插件
    // require('@tailwindcss/forms'),
    // require('@tailwindcss/typography'),
    // require('@tailwindcss/aspect-ratio'),
  ],
}
```

### CSS 层级和自定义组件

在 `assets/css/main.css` 中使用 `@layer` 指令组织样式：

```css
/* Tailwind CSS 基础指令 */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义组件样式 */
@layer components {
  .btn-primary {
    @apply bg-primary-500 hover:bg-primary-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }
  
  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-md p-6 border border-gray-200;
  }
}

/* 自定义工具类 */
@layer utilities {
  .text-shadow {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .bg-gradient-primary {
    background: linear-gradient(135deg, theme('colors.primary.500'), theme('colors.primary.600'));
  }
}
```

## 🎨 使用示例

### 基础组件示例

```vue
<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
    <!-- 使用自定义按钮组件 -->
    <button class="btn-primary">
      Primary Button
    </button>
    
    <!-- 使用自定义卡片组件 -->
    <div class="card">
      <h3 class="text-xl font-semibold text-gray-800 mb-4">卡片标题</h3>
      <p class="text-gray-600">卡片内容...</p>
    </div>
    
    <!-- 响应式网格 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <!-- 网格项目 -->
    </div>
  </div>
</template>
```

### 响应式设计

```vue
<template>
  <div class="container mx-auto px-4">
    <!-- 响应式文本大小 -->
    <h1 class="text-2xl md:text-4xl lg:text-6xl font-bold">
      响应式标题
    </h1>
    
    <!-- 响应式布局 -->
    <div class="flex flex-col md:flex-row gap-4">
      <div class="w-full md:w-1/2 lg:w-1/3">侧边栏</div>
      <div class="w-full md:w-1/2 lg:w-2/3">主内容</div>
    </div>
  </div>
</template>
```

## 🔧 高级配置

### 与 PostCSS 集成

Nuxt3 内置了 PostCSS 支持，可以在 `nuxt.config.ts` 中配置：

```typescript
export default defineNuxtConfig({
  postcss: {
    plugins: {
      'postcss-nested': {},
      'postcss-custom-media': {},
      // Tailwind CSS 会自动添加
    }
  },
})
```

### 生产环境优化

Tailwind CSS 会自动进行 CSS 清理（purging），只包含实际使用的样式类。确保在 `tailwind.config.js` 中正确配置 `content` 路径。

### 暗色模式支持

```javascript
// tailwind.config.js
module.exports = {
  darkMode: 'class', // 或 'media'
  // ... 其他配置
}
```

```vue
<template>
  <div class="bg-white dark:bg-gray-900 text-gray-900 dark:text-white">
    <h1 class="text-xl font-bold">支持暗色模式的内容</h1>
  </div>
</template>
```

## 📁 项目结构

```
nuxt3-learning-demo/
├── assets/
│   └── css/
│       └── main.css          # Tailwind 指令和自定义样式
├── components/               # Vue 组件
├── layouts/                  # 布局文件
├── pages/                    # 页面文件
│   └── tailwind-test.vue     # Tailwind 测试页面
├── tailwind.config.js        # Tailwind 配置文件
├── nuxt.config.ts           # Nuxt 配置文件
└── package.json             # 项目依赖
```

## 🧪 测试和验证

访问 `/tailwind-test` 页面来验证 Tailwind CSS 的集成效果，该页面包含：

- 颜色系统测试
- 自定义组件测试
- 响应式设计验证
- 动画和交互效果
- 自定义配置验证

## 📚 最佳实践

1. **使用语义化的组件类**：在 `@layer components` 中定义可重用的组件样式
2. **保持配置文件整洁**：只添加项目实际需要的自定义配置
3. **利用响应式设计**：使用 Tailwind 的响应式前缀（sm:, md:, lg:, xl:）
4. **性能优化**：确保 `content` 路径配置正确，以便进行有效的 CSS 清理
5. **团队协作**：建立统一的设计系统和组件库

## 🔗 相关资源

- [Tailwind CSS 官方文档](https://tailwindcss.com/docs)
- [@nuxtjs/tailwindcss 模块文档](https://tailwindcss.nuxtjs.org/)
- [Nuxt3 官方文档](https://nuxt.com/)

## 🐛 常见问题

### 样式不生效
- 检查 `tailwind.config.js` 中的 `content` 路径配置
- 确认 CSS 文件中包含了 `@tailwind` 指令
- 重启开发服务器

### 自定义样式冲突
- 使用 `@layer` 指令正确组织样式
- 检查 CSS 特异性和优先级
- 使用 `!important` 修饰符（谨慎使用）

---

*文档生成时间：2025-01-04*
*Tailwind CSS 版本：3.4.17*
*@nuxtjs/tailwindcss 版本：6.14.0*
