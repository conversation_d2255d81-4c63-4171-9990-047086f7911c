<script setup>
import '~/assets/css/first.css'

// useHead({
// 	title: "this's abouts",
// 	titleTemplate: (titleChunk) => {
// 		console.log('[30m [ titleChunk ]-5 [0m', titleChunk)
// 		return titleChunk ? `${titleChunk} %separator %name ` : "Rex's Nuxt"
// 	},
// 	templateParams: {
// 		name: '站点标题',
// 		separator: '-',
// 	},
// })
</script>

<template>
	<div>
		<h1>欢迎来到首页</h1>
		<ButtonText> 这是一个自动导入的组件 </ButtonText>
		<nuxt-link to="/abouts"> 跳转到关于页面 </nuxt-link>
	</div>
</template>

<style lang="less" scoped>
@import url('~/assets/css/second.css');

div {
	h1 {
		background-color: palevioletred;
	}

	.text-btn {
		height: 100px;
		cursor: pointer;
	}
}
</style>
