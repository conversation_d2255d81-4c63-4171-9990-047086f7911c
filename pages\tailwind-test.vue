<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4">
    <div class="max-w-4xl mx-auto">
      <!-- 页面标题 -->
      <div class="text-center mb-12">
        <h1 class="text-4xl font-bold text-gray-900 mb-4 text-shadow">
          🎨 Tailwind CSS 集成测试
        </h1>
        <p class="text-lg text-gray-600 max-w-2xl mx-auto">
          这个页面用于测试 Tailwind CSS 在 Nuxt3 项目中的集成效果，包括响应式设计、自定义组件和动画效果。
        </p>
      </div>

      <!-- 功能展示网格 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
        <!-- 颜色系统测试 -->
        <div class="card animate-fade-in">
          <h3 class="text-xl font-semibold text-gray-800 mb-4">🎨 颜色系统</h3>
          <div class="space-y-2">
            <div class="flex items-center space-x-2">
              <div class="w-4 h-4 bg-primary-500 rounded"></div>
              <span class="text-sm">Primary 500</span>
            </div>
            <div class="flex items-center space-x-2">
              <div class="w-4 h-4 bg-primary-600 rounded"></div>
              <span class="text-sm">Primary 600</span>
            </div>
            <div class="flex items-center space-x-2">
              <div class="w-4 h-4 bg-gray-300 rounded"></div>
              <span class="text-sm">Gray 300</span>
            </div>
          </div>
        </div>

        <!-- 按钮组件测试 -->
        <div class="card animate-fade-in" style="animation-delay: 0.1s">
          <h3 class="text-xl font-semibold text-gray-800 mb-4">🔘 按钮组件</h3>
          <div class="space-y-3">
            <button class="btn-primary w-full">
              Primary Button
            </button>
            <button class="btn-secondary w-full">
              Secondary Button
            </button>
            <button class="bg-gradient-primary text-white font-medium py-2 px-4 rounded-lg w-full hover:scale-105 transform transition-all duration-200">
              Gradient Button
            </button>
          </div>
        </div>

        <!-- 响应式测试 -->
        <div class="card animate-fade-in md:col-span-2 lg:col-span-1" style="animation-delay: 0.2s">
          <h3 class="text-xl font-semibold text-gray-800 mb-4">📱 响应式设计</h3>
          <div class="space-y-2 text-sm">
            <div class="block sm:hidden text-red-600">📱 手机视图 (< 640px)</div>
            <div class="hidden sm:block md:hidden text-yellow-600">📱 平板视图 (640px - 768px)</div>
            <div class="hidden md:block lg:hidden text-blue-600">💻 小桌面 (768px - 1024px)</div>
            <div class="hidden lg:block text-green-600">🖥️ 大桌面 (> 1024px)</div>
          </div>
        </div>
      </div>

      <!-- 动画和交互测试 -->
      <div class="card mb-8 animate-slide-up">
        <h3 class="text-2xl font-semibold text-gray-800 mb-6">✨ 动画和交互效果</h3>
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <div class="bg-primary-100 p-4 rounded-lg hover:bg-primary-200 transition-colors duration-300 cursor-pointer">
            <div class="text-primary-600 font-medium">Hover 颜色变化</div>
          </div>
          <div class="bg-green-100 p-4 rounded-lg hover:scale-105 transform transition-transform duration-300 cursor-pointer">
            <div class="text-green-600 font-medium">Hover 缩放效果</div>
          </div>
          <div class="bg-purple-100 p-4 rounded-lg hover:shadow-lg transition-shadow duration-300 cursor-pointer">
            <div class="text-purple-600 font-medium">Hover 阴影效果</div>
          </div>
          <div class="bg-orange-100 p-4 rounded-lg hover:rotate-2 transform transition-transform duration-300 cursor-pointer">
            <div class="text-orange-600 font-medium">Hover 旋转效果</div>
          </div>
        </div>
      </div>

      <!-- 自定义间距和字体测试 -->
      <div class="card">
        <h3 class="text-2xl font-semibold text-gray-800 mb-6">📏 自定义配置测试</h3>
        <div class="space-y-4">
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="font-medium text-gray-700 mb-2">自定义间距 (spacing-18 = 4.5rem)</h4>
            <div class="bg-blue-200 h-4 w-18 rounded"></div>
          </div>
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="font-medium text-gray-700 mb-2">自定义间距 (spacing-88 = 22rem)</h4>
            <div class="bg-green-200 h-4 w-88 rounded max-w-full"></div>
          </div>
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="font-medium text-gray-700 mb-2">自定义字体 (font-sans = Inter)</h4>
            <p class="font-sans text-lg">这段文字使用了自定义的 Inter 字体</p>
          </div>
        </div>
      </div>

      <!-- 返回首页按钮 -->
      <div class="text-center mt-12">
        <NuxtLink to="/" class="btn-primary inline-flex items-center space-x-2">
          <span>← 返回首页</span>
        </NuxtLink>
      </div>
    </div>
  </div>
</template>

<script setup>
// 设置页面元数据
useHead({
  title: 'Tailwind CSS 集成测试',
  meta: [
    { name: 'description', content: '测试 Tailwind CSS 在 Nuxt3 项目中的集成效果' }
  ]
})
</script>

<style scoped>
/* 这里可以添加页面特定的样式，但主要依赖 Tailwind CSS */
</style>
