export default defineNuxtConfig({
  compatibilityDate: '2025-05-15',
  devtools: { enabled: true },
  modules: [
    '@nuxt/eslint',
    '@nuxtjs/i18n'
  ],

  i18n: {
    locales: [
      { code: 'en', language: 'en-US' },
      { code: 'fr', language: 'fr-FR' }
    ],
    defaultLocale: 'en',
  },
    css: ['~/assets/css/main.css'],
  // 内置的postcss
  postcss: {
    plugins: {
      'postcss-nested': {},
      'postcss-custom-media': {}
    }
  },

  devServer: {
    port: process.env.SERVER_PORT as unknown as number || 8080,
  },

  app: {
    baseURL: process.env.PUBLIC_PATH || '/',
    head: {
      // title: 'Rex\'s Nuxt',
      htmlAttrs: {
        lang: 'en',
      },
      link: [
        { rel: 'icon', type: 'image/png', href: '/image.png' }
      ],
      charset: 'utf-16',
      viewport: 'width=device-width, initial-scale=2, maximum-scale=1',

    },
    pageTransition: { name: 'page', mode: 'out-in' }
  }

})